import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
interface Task {
  id: string;
  title: string;
  completed: boolean;
  startTime?: string;
  endTime?: string;
  isRecurring: boolean;
  isFlexible: boolean;
  notes?: string;
}

interface User {
  name: string;
  avatar: string;
  rank: number;
  coins: number;
  chips: number;
}

interface Friend {
  id: string;
  name: string;
  rank: number;
  avatar: string;
}

interface GameStats {
  coins: number;
  chips: number;
  cardEffects: string[];
  availablePacks: string[];
}

export default function Index() {
  const [currentView, setCurrentView] = useState<'today' | 'calendar'>('today');
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      title: 'Morning workout',
      completed: false,
      startTime: '10:00',
      endTime: '11:00',
      isRecurring: true,
      isFlexible: false,
    },
    {
      id: '2',
      title: 'Review project proposal',
      completed: false,
      isRecurring: false,
      isFlexible: true,
    },
    {
      id: '3',
      title: 'Team standup',
      completed: true,
      startTime: '09:00',
      endTime: '09:30',
      isRecurring: true,
      isFlexible: false,
    },
  ]);

  const [user] = useState<User>({
    name: 'Player',
    avatar: '👤',
    rank: 5,
    coins: 320,
    chips: 5,
  });

  const [gameStats] = useState<GameStats>({
    coins: 320,
    chips: 5,
    cardEffects: ['+25% Daily Bonus', 'Streak Multiplier x2'],
    availablePacks: ['Epic Pack', 'Rare Pack'],
  });

  const [friends] = useState<Friend[]>([
    { id: '1', name: 'Bob', rank: 3, avatar: '👨' },
    { id: '2', name: 'Alice', rank: 7, avatar: '👩' },
  ]);

  // New task form state
  const [newTask, setNewTask] = useState({
    title: '',
    startTime: '',
    endTime: '',
    isRecurring: false,
    isFlexible: true,
    notes: '',
  });

  const toggleTask = (taskId: string) => {
    setTasks(tasks.map(task =>
      task.id === taskId
        ? { ...task, completed: !task.completed }
        : task
    ));

    // TODO: API call to update task completion
    // TODO: Award coins for task completion
    // TODO: Check for streak bonuses
  };

  const addTask = () => {
    if (!newTask.title.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    const task: Task = {
      id: Date.now().toString(),
      title: newTask.title,
      completed: false,
      startTime: newTask.startTime || undefined,
      endTime: newTask.endTime || undefined,
      isRecurring: newTask.isRecurring,
      isFlexible: newTask.isFlexible,
      notes: newTask.notes || undefined,
    };

    setTasks([...tasks, task]);
    setNewTask({
      title: '',
      startTime: '',
      endTime: '',
      isRecurring: false,
      isFlexible: true,
      notes: '',
    });
    setShowAddTaskModal(false);

    // TODO: API call to create task
  };

  const formatTime = (time: string) => {
    if (!time) return '';
    return time;
  };

  const getTaskTimeDisplay = (task: Task) => {
    if (task.startTime && task.endTime) {
      return `⏰ ${formatTime(task.startTime)} – ${formatTime(task.endTime)}`;
    }
    if (task.isRecurring) {
      return '🔁 Daily';
    }
    if (task.isFlexible) {
      return '⏳ Flexible';
    }
    return '';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.avatar}>{user.avatar}</Text>
            <View>
              <Text style={styles.userName}>{user.name}</Text>
              <Text style={styles.userRank}>🏅 Rank {user.rank}</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.settingsButton}>
            <Text style={styles.settingsIcon}>⚙️</Text>
          </TouchableOpacity>
        </View>

        {/* View Toggle */}
        <View style={styles.viewToggle}>
          <TouchableOpacity
            style={[styles.toggleButton, currentView === 'today' && styles.activeToggle]}
            onPress={() => setCurrentView('today')}
          >
            <Text style={[styles.toggleText, currentView === 'today' && styles.activeToggleText]}>
              Today View
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.toggleButton, currentView === 'calendar' && styles.activeToggle]}
            onPress={() => setCurrentView('calendar')}
          >
            <Text style={[styles.toggleText, currentView === 'calendar' && styles.activeToggleText]}>
              Calendar 📅
            </Text>
          </TouchableOpacity>
        </View>

        {/* To-Do List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📋 To-Do List</Text>
          {tasks.map((task) => (
            <TouchableOpacity
              key={task.id}
              style={styles.taskItem}
              onPress={() => toggleTask(task.id)}
            >
              <Text style={styles.taskCheckbox}>
                {task.completed ? '✅' : '☐'}
              </Text>
              <View style={styles.taskContent}>
                <Text style={[styles.taskTitle, task.completed && styles.completedTask]}>
                  {task.title}
                </Text>
                <Text style={styles.taskTime}>
                  {getTaskTimeDisplay(task)}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Add Task Button */}
        <TouchableOpacity
          style={styles.addTaskButton}
          onPress={() => setShowAddTaskModal(true)}
        >
          <Text style={styles.addTaskText}>➕ Add Task</Text>
        </TouchableOpacity>

        {/* Game Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎮 Game Summary</Text>
          <View style={styles.gameStats}>
            <View style={styles.statRow}>
              <Text style={styles.statText}>Coins: 💰 {gameStats.coins}</Text>
              <Text style={styles.statText}>Chips: 🪙 {gameStats.chips}</Text>
            </View>
            <Text style={styles.statText}>
              Card Effects: {gameStats.cardEffects.join(', ')}
            </Text>
            <Text style={styles.statText}>
              Pack Drop: 🃏 "{gameStats.availablePacks[0]}" in shop
            </Text>
          </View>
        </View>

        {/* Friends & Sharing */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👥 Friends & Sharing</Text>
          {friends.map((friend) => (
            <View key={friend.id} style={styles.friendItem}>
              <View style={styles.friendInfo}>
                <Text style={styles.friendAvatar}>{friend.avatar}</Text>
                <Text style={styles.friendName}>{friend.name}</Text>
                <Text style={styles.friendRank}>🏆 Rank {friend.rank}</Text>
              </View>
              <TouchableOpacity style={styles.tradeButton}>
                <Text style={styles.tradeButtonText}>+ Trade Cards</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Add Task Modal */}
      <Modal
        visible={showAddTaskModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddTaskModal(false)}>
              <Text style={styles.modalCloseButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add New Task</Text>
            <TouchableOpacity onPress={addTask}>
              <Text style={styles.modalSaveButton}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Task Title *</Text>
              <TextInput
                style={styles.textInput}
                value={newTask.title}
                onChangeText={(text) => setNewTask({ ...newTask, title: text })}
                placeholder="Enter task title"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Start Time</Text>
              <TextInput
                style={styles.textInput}
                value={newTask.startTime}
                onChangeText={(text) => setNewTask({ ...newTask, startTime: text })}
                placeholder="e.g., 10:00"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>End Time</Text>
              <TextInput
                style={styles.textInput}
                value={newTask.endTime}
                onChangeText={(text) => setNewTask({ ...newTask, endTime: text })}
                placeholder="e.g., 11:00"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.switchGroup}>
              <Text style={styles.inputLabel}>Recurring Daily</Text>
              <Switch
                value={newTask.isRecurring}
                onValueChange={(value) => setNewTask({ ...newTask, isRecurring: value })}
              />
            </View>

            <View style={styles.switchGroup}>
              <Text style={styles.inputLabel}>Flexible Timing</Text>
              <Switch
                value={newTask.isFlexible}
                onValueChange={(value) => setNewTask({ ...newTask, isFlexible: value })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Notes</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={newTask.notes}
                onChangeText={(text) => setNewTask({ ...newTask, notes: text })}
                placeholder="Add any notes or insights"
                placeholderTextColor="#999"
                multiline
                numberOfLines={4}
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    fontSize: 32,
    marginRight: 12,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  userRank: {
    fontSize: 14,
    color: '#666',
  },
  settingsButton: {
    padding: 8,
  },
  settingsIcon: {
    fontSize: 24,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  toggleButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  activeToggle: {
    backgroundColor: '#007AFF',
  },
  toggleText: {
    fontSize: 16,
    color: '#333',
  },
  activeToggleText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  taskCheckbox: {
    fontSize: 20,
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },
  completedTask: {
    textDecorationLine: 'line-through',
    color: '#999',
  },
  taskTime: {
    fontSize: 14,
    color: '#666',
  },
  addTaskButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  addTaskText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  gameStats: {
    gap: 8,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statText: {
    fontSize: 14,
    color: '#333',
  },
  friendItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  friendInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  friendAvatar: {
    fontSize: 24,
    marginRight: 12,
  },
  friendName: {
    fontSize: 16,
    color: '#333',
    marginRight: 12,
  },
  friendRank: {
    fontSize: 14,
    color: '#666',
  },
  tradeButton: {
    backgroundColor: '#34C759',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  tradeButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalCloseButton: {
    color: '#007AFF',
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalSaveButton: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  switchGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
});
